// Countdown functionality for closed.html
function initializeCountdown() {
    const countdownDisplay = document.getElementById('countdownDisplay');
    const countdownContainer = document.getElementById('countdownContainer');

    console.log('Initializing countdown...');

    // Check if chrome.storage is available
    if (!chrome || !chrome.storage) {
        console.error('Chrome storage API not available');
        // Fallback to default 60 minutes
        const blockingMinutes = 60;
        const unblockAt = new Date(Date.now() + (blockingMinutes * 60 * 1000));
        console.log('Using fallback: 60 minutes, ending at:', unblockAt);
        startCountdown(countdownDisplay, countdownContainer, unblockAt);
        return;
    }

    // Get the blocking period end time from local storage
    chrome.storage.local.get(['blockingPeriodEndTime'], (data) => {
        console.log('Local storage data:', data);

        if (data.blockingPeriodEndTime && data.blockingPeriodEndTime > Date.now()) {
            // Use existing blocking period end time
            const unblockAt = new Date(data.blockingPeriodEndTime);
            console.log('Using existing blocking period end time:', unblockAt);
            startCountdown(countdownDisplay, countdownContainer, unblockAt);
        } else {
            console.log('No valid blocking period found. This page should not be shown without an active blocking period.');
            // Don't create a new blocking period - this indicates an error state
            // Show error message and redirect back
            countdownDisplay.textContent = 'Error: No active blocking period';
            setTimeout(() => {
                console.log('Redirecting back due to no active blocking period');
                window.history.back();
            }, 2000);
        }
    });
}

function startCountdown(countdownDisplay, countdownContainer, unblockAt) {
    console.log('Starting countdown to:', unblockAt);
    updateCountdown(countdownDisplay, countdownContainer, unblockAt);

    // Update every second
    const interval = setInterval(() => {
        updateCountdown(countdownDisplay, countdownContainer, unblockAt);
    }, 1000);

    // Store interval ID for cleanup
    window.countdownInterval = interval;
}

function updateCountdown(displayElement, containerElement, unblockTime) {
    try {
        const now = new Date();
        const timeRemaining = unblockTime.getTime() - now.getTime();

        console.log('Updating countdown - Time remaining:', timeRemaining, 'ms');

        if (timeRemaining <= 0) {
            // Time expired
            displayElement.textContent = '00:00';
            containerElement.classList.add('countdown-expired');
            displayElement.parentElement.querySelector('.countdown-title').textContent = 'Access Restored!';
            displayElement.parentElement.querySelector('.countdown-subtitle').textContent = 'You can now refresh this page to continue browsing';

            // Clear the interval
            if (window.countdownInterval) {
                clearInterval(window.countdownInterval);
            }

            // Optionally auto-refresh after a few seconds
            setTimeout(() => {
                window.location.reload();
            }, 3000);

            return;
        }

        // Calculate hours, minutes, and seconds
        const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
        const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

        // Format display
        let timeString;
        if (hours > 0) {
            timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        } else {
            timeString = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        }

        displayElement.textContent = timeString;
        console.log('Countdown updated to:', timeString);

    } catch (error) {
        console.error('Error updating countdown:', error);
        displayElement.textContent = 'Error';
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initializeCountdown);

// Also initialize immediately in case DOMContentLoaded already fired
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCountdown);
} else {
    initializeCountdown();
}
